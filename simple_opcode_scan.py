#!/usr/bin/env python3

def scan_opcodes(filename):
    """Simple scan for potential opcodes in the file"""
    with open(filename, 'rb') as f:
        data = f.read()
    
    print(f"File size: {len(data)} bytes")
    
    # Look for patterns that might be opcodes
    # LuaJIT instructions are typically 4 bytes
    opcodes = set()
    
    # Scan every 4th byte as potential opcode
    for i in range(0, len(data) - 3, 4):
        opcode = data[i]
        if opcode <= 150:  # Reasonable upper bound for opcodes
            opcodes.add(opcode)
    
    print(f"\nPotential opcodes found (scanning every 4th byte):")
    for opcode in sorted(opcodes):
        print(f"  0x{opcode:02x} ({opcode})")
    
    # Also scan for 0x68 specifically
    count_68 = data.count(0x68)
    print(f"\nOccurrences of 0x68 in file: {count_68}")
    
    # Find positions of 0x68
    positions = []
    pos = 0
    while True:
        pos = data.find(0x68, pos)
        if pos == -1:
            break
        positions.append(pos)
        pos += 1
    
    print(f"Positions of 0x68: {positions[:10]}...")  # Show first 10

if __name__ == "__main__":
    scan_opcodes("modded.luac")
