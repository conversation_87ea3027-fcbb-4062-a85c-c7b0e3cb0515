# LuaJIT Opcode Mapping
# Format: Index: Address = BC_OPCODE (confidence)

0x0: 0x0000000000BEBC10 = BC_ISLT (LOW)
0x1: 0x0000000000BEBC90 = BC_ISGE (LOW)
0x2: 0x0000000000BEBD10 = BC_ISLE (LOW)
0x3: 0x0000000000BEBD90 = BC_ISGT (LOW)
0x4: 0x0000000000BEBE10 = BC_ISEQV (LOW)
0x5: 0x0000000000BEBE94 = BC_ISNEV (LOW)
0x6: 0x0000000000BEBF14 = BC_ISEQS (LOW)
0x7: 0x0000000000BEBF64 = BC_ISNES (LOW)
0x8: 0x0000000000BEBFB4 = BC_ISEQN (LOW)
0x9: 0x0000000000BEC040 = BC_ISNEN (LOW)
0xa: 0x0000000000BEC0CC = BC_ISEQP (LOW)
0xb: 0x0000000000BEC110 = BC_ISNEP (LOW)
0xc: 0x0000000000BEC154 = BC_ISFC (LOW)
0xd: 0x0000000000BEC194 = BC_ISTC (LOW)
0xe: 0x0000000000BEC1D4 = BC_ISF (LOW)
0xf: 0x0000000000BEC20C = BC_IST (LOW)
0x10: 0x0000000000BEC244 = BC_ISTYPE (LOW)
0x11: 0x0000000000BEC268 = BC_ISNUM (LOW)
0x12: 0x0000000000BEC28C = BC_NOT (LOW)
0x13: 0x0000000000BEC2BC = BC_MOV (LOW)
0x14: 0x0000000000BEC2DC = BC_LEN (LOW)
0x15: 0x0000000000BEC324 = BC_UNM (LOW)
0x16: 0x0000000000BEC368 = BC_ADDVN (LOW)
0x17: 0x0000000000BEC3D4 = BC_SUBVN (LOW)
0x18: 0x0000000000BEC440 = BC_MULVN (LOW)
0x19: 0x0000000000BEC4B4 = BC_DIVVN (LOW)
0x1a: 0x0000000000BEC4FC = BC_MODVN (LOW)
0x1b: 0x0000000000BEC570 = BC_ADDNV (LOW)
0x1c: 0x0000000000BEC5DC = BC_SUBNV (LOW)
0x1d: 0x0000000000BEC648 = BC_MULNV (LOW)
0x1e: 0x0000000000BEC6BC = BC_DIVNV (LOW)
0x1f: 0x0000000000BEC704 = BC_MODNV (LOW)
0x20: 0x0000000000BEC778 = BC_ADDVV (LOW)
0x21: 0x0000000000BEC7E4 = BC_SUBVV (LOW)
0x22: 0x0000000000BEC850 = BC_MULVV (LOW)
0x23: 0x0000000000BEC8C4 = BC_DIVVV (LOW)
0x24: 0x0000000000BEC90C = BC_MODVV (LOW)
0x25: 0x0000000000BEC980 = BC_POW (LOW)
0x26: 0x0000000000BEC9C8 = BC_CAT (LOW)
0x27: 0x0000000000BECA14 = BC_UGET (LOW)
0x28: 0x0000000000BECA48 = BC_USETV (LOW)
0x29: 0x0000000000BECAB8 = BC_USETS (LOW)
0x2a: 0x0000000000BECB20 = BC_USETN (LOW)
0x2b: 0x0000000000BECB54 = BC_USETP (LOW)
0x2c: 0x0000000000BECB88 = BC_UCLO (LOW)
0x2d: 0x0000000000BECBC4 = BC_FNEW (LOW)
0x2e: 0x0000000000BECC0C = BC_KSTR (LOW)
0x2f: 0x0000000000BECC38 = BC_KCDATA (LOW)
0x30: 0x0000000000BECC64 = BC_KSHORT (LOW)
0x31: 0x0000000000BECC88 = BC_KNUM (LOW)
0x32: 0x0000000000BECCA8 = BC_KPRI (LOW)
0x33: 0x0000000000BECCC8 = BC_KNIL (LOW)
0x34: 0x0000000000BECCF8 = BC_TNEW (LOW)
0x35: 0x0000000000BECD58 = BC_TDUP (LOW)
0x36: 0x0000000000BECDAC = BC_GGET (LOW)
0x37: 0x0000000000BECDC4 = BC_GSET (LOW)
0x38: 0x0000000000BECDDC = BC_TGETV (LOW)
0x39: 0x0000000000BECE68 = BC_TGETS (LOW)
0x3a: 0x0000000000BECE8C = BC_TGETS_Z (LOW)
0x3b: 0x0000000000BECEFC = BC_TGETB (LOW)
0x3c: 0x0000000000BECF68 = BC_TGETR (LOW)
0x3d: 0x0000000000BECFB0 = BC_TSETV (LOW)
0x3e: 0x0000000000BED060 = BC_TSETS (LOW)
0x3f: 0x0000000000BED084 = BC_TSETS_Z (LOW)
0x40: 0x0000000000BED15C = BC_TSETB (LOW)
0x41: 0x0000000000BED1EC = BC_TSETM (LOW)
0x42: 0x0000000000BED280 = BC_TSETR (LOW)
0x43: 0x0000000000BED2E8 = BC_CALLM (LOW)
0x44: 0x0000000000BED2F8 = BC_CALL (LOW)
0x45: 0x0000000000BED2FC = BC_CALL_Z (LOW)
0x46: 0x0000000000BED340 = BC_CALLMT (LOW)
0x47: 0x0000000000BED34C = BC_CALLT (LOW)
0x48: 0x0000000000BED350 = BC_CALLT1_Z (LOW)
0x49: 0x0000000000BED374 = BC_CALLT2_Z (LOW)
0x4a: 0x0000000000BED408 = BC_ITERC (LOW)
0x4b: 0x0000000000BED474 = BC_ITERN (LOW)
0x4c: 0x0000000000BED518 = BC_VARG (LOW)
0x4d: 0x0000000000BED5D4 = BC_ISNEXT (LOW)
0x4e: 0x0000000000BED674 = BC_RETM (LOW)
0x4f: 0x0000000000BED688 = BC_RET (LOW)
0x50: 0x0000000000BED698 = BC_RET_Z (LOW)
0x51: 0x0000000000BED730 = BC_RET0 (LOW)
0x52: 0x0000000000BED798 = BC_RET1 (LOW)
0x53: 0x0000000000BED808 = BC_FORI (LOW)
0x54: 0x0000000000BED898 = BC_JFORL (LOW)
0x55: 0x0000000000BED934 = BC_FORL (LOW)
0x56: 0x0000000000BED950 = BC_IFORL2 (LOW)
0x57: 0x0000000000BED9DC = BC_IFORL (LOW)
0x58: 0x0000000000BEDA60 = BC_ITERL (LOW)
0x59: 0x0000000000BEDAB0 = BC_IITERL (LOW)
0x5a: 0x0000000000BEDAE0 = BC_LOOP (LOW)
0x5b: 0x0000000000BEDB14 = BC_JLOOP (LOW)
0x5c: 0x0000000000BEDB38 = BC_JMP (LOW)
0x5d: 0x0000000000BEDB58 = BC_FUNCF (LOW)
0x5e: 0x0000000000BEDBB4 = BC_IFUNCF (LOW)
0x5f: 0x0000000000BEDBE4 = BC_JFUNCF (LOW)
0x60: 0x0000000000BEDC68 = BC_FUNCC (LOW)
0x61: 0x0000000000BEDCB4 = BC_FUNCCW (LOW)
