#!/usr/bin/env python3

import ljd
import ljd.rawdump.code
import ljd.bytecode.instructions as ins

# Test the opcode initialization
ljd.CURRENT_VERSION = 2.1

from ljd.rawdump.luajit.v2_1.luajit_opcode import _OPCODES as opcodes

print("Testing opcode initialization...")
print(f"Number of opcodes: {len(opcodes)}")

# Initialize opcodes
ljd.rawdump.code.init(opcodes)

# Check for None opcodes
none_opcodes = []
for opcode, instruction in opcodes:
    if instruction.opcode is None:
        none_opcodes.append((opcode, instruction.name))
    else:
        print(f"Opcode {opcode:02x}: {instruction.name} -> {instruction.opcode}")

if none_opcodes:
    print("\nFound None opcodes:")
    for opcode, name in none_opcodes:
        print(f"  {opcode:02x}: {name}")
else:
    print("\nAll opcodes initialized successfully!")

# Check for duplicates
opcode_map = {}
duplicates = []
for opcode, instruction in opcodes:
    if opcode in opcode_map:
        duplicates.append((opcode, opcode_map[opcode], instruction.name))
    else:
        opcode_map[opcode] = instruction.name

if duplicates:
    print("\nFound duplicate opcodes:")
    for opcode, name1, name2 in duplicates:
        print(f"  {opcode:02x}: {name1} and {name2}")
else:
    print("\nNo duplicate opcodes found!")
