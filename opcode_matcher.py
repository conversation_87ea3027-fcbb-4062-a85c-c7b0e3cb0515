#!/usr/bin/env python3

import re
import sys
from collections import defaultdict

def parse_ida_opcodes(filename):
    """Parse IDA Pro opcode.txt file to extract dispatch array entries"""
    opcodes = {}
    current_function = None
    current_address = None
    current_instructions = []
    
    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
                
            # Look for function definitions like "BC_ISLT"
            if re.match(r'LOAD:\w+ (BC_\w+)', line):
                # Save previous function if exists
                if current_function and current_address:
                    opcodes[current_address] = {
                        'name': current_function,
                        'instructions': current_instructions.copy()
                    }
                
                # Start new function
                match = re.search(r'LOAD:(\w+) (BC_\w+)', line)
                if match:
                    current_address = match.group(1)
                    current_function = match.group(2)
                    current_instructions = []
            
            # Extract assembly instructions
            elif re.match(r'LOAD:\w+\s+\w+', line):
                # Parse instruction line
                match = re.search(r'LOAD:\w+\s+(.+)', line)
                if match:
                    instruction = match.group(1).strip()
                    # Clean up instruction (remove comments, etc.)
                    instruction = re.sub(r';.*$', '', instruction).strip()
                    if instruction and current_function:
                        current_instructions.append(instruction)
    
    # Save last function
    if current_function and current_address:
        opcodes[current_address] = {
            'name': current_function,
            'instructions': current_instructions.copy()
        }
    
    return opcodes

def parse_vm_source(filename):
    """Parse vm_arm64.dasc to extract bytecode instruction patterns"""
    patterns = {}
    current_case = None
    current_instructions = []
    in_case = False
    
    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            
            # Look for case statements
            if line.startswith('case BC_'):
                # Save previous case
                if current_case and current_instructions:
                    patterns[current_case] = current_instructions.copy()
                
                # Extract case names
                cases = re.findall(r'BC_\w+', line)
                if cases:
                    current_case = cases[0]  # Take first case
                    current_instructions = []
                    in_case = True
            
            # Look for break to end case
            elif line == 'break;' and in_case:
                if current_case and current_instructions:
                    patterns[current_case] = current_instructions.copy()
                in_case = False
                current_case = None
                current_instructions = []
            
            # Extract assembly instructions (lines starting with |)
            elif line.startswith('|') and in_case:
                # Clean up the instruction
                instruction = line[1:].strip()
                # Remove comments
                instruction = re.sub(r'//.*$', '', instruction).strip()
                if instruction and not instruction.startswith('.'):
                    current_instructions.append(instruction)
    
    return patterns

def normalize_instruction(instr):
    """Normalize instruction for comparison"""
    # Remove extra whitespace
    instr = re.sub(r'\s+', ' ', instr.strip())
    
    # Normalize register names (convert ARM64 assembly syntax)
    # Convert X19 -> x19, W16 -> w16, etc.
    instr = re.sub(r'\bX(\d+)\b', r'x\1', instr)
    instr = re.sub(r'\bW(\d+)\b', r'w\1', instr)
    
    # Normalize immediate values
    instr = re.sub(r'#0x([0-9A-Fa-f]+)', r'#\1', instr)
    
    # Remove trailing commas and spaces
    instr = instr.rstrip(', ')
    
    return instr.lower()

def match_instruction_sequences(ida_instrs, src_instrs):
    """Match instruction sequences between IDA and source"""
    if not ida_instrs or not src_instrs:
        return 0
    
    # Normalize all instructions
    ida_norm = [normalize_instruction(instr) for instr in ida_instrs[:10]]  # First 10 instructions
    src_norm = [normalize_instruction(instr) for instr in src_instrs[:10]]
    
    # Count exact matches
    matches = 0
    for i, ida_instr in enumerate(ida_norm):
        for j, src_instr in enumerate(src_norm):
            if ida_instr == src_instr:
                matches += 1
                break
    
    return matches

def find_best_matches(ida_opcodes, vm_patterns):
    """Find best matches between IDA opcodes and VM patterns"""
    results = {}
    
    for address, ida_data in ida_opcodes.items():
        ida_name = ida_data['name']
        ida_instrs = ida_data['instructions']
        
        best_match = None
        best_score = 0
        
        # Try to match with source patterns
        for pattern_name, src_instrs in vm_patterns.items():
            score = match_instruction_sequences(ida_instrs, src_instrs)
            
            if score > best_score:
                best_score = score
                best_match = pattern_name
        
        results[address] = {
            'ida_name': ida_name,
            'best_match': best_match,
            'score': best_score,
            'instructions': ida_instrs[:5]  # Keep first 5 for debugging
        }
    
    return results

def main():
    print("=== LuaJIT Opcode Matcher ===")
    
    # Parse input files
    print("Parsing IDA Pro opcodes...")
    ida_opcodes = parse_ida_opcodes('ida pro opcode.txt')
    print(f"Found {len(ida_opcodes)} IDA opcodes")
    
    print("Parsing VM source patterns...")
    vm_patterns = parse_vm_source('vm_arm64.dasc')
    print(f"Found {len(vm_patterns)} VM patterns")
    
    # Find matches
    print("Matching instruction sequences...")
    matches = find_best_matches(ida_opcodes, vm_patterns)
    
    # Generate output
    print("\n=== Opcode Mapping Results ===")
    
    # Sort by address
    sorted_addresses = sorted(matches.keys(), key=lambda x: int(x, 16))
    
    opcode_index = 0
    with open('opcode_mapping.txt', 'w') as f:
        f.write("# LuaJIT Opcode Mapping\n")
        f.write("# Format: Index: Address = BC_OPCODE (confidence)\n\n")
        
        for address in sorted_addresses:
            match_data = matches[address]
            ida_name = match_data['ida_name']
            best_match = match_data['best_match']
            score = match_data['score']
            
            # Determine confidence
            if best_match and score >= 3:
                confidence = "HIGH"
                final_name = best_match
            elif best_match and score >= 1:
                confidence = "MEDIUM"
                final_name = best_match
            else:
                confidence = "LOW"
                final_name = ida_name  # Fall back to IDA name
            
            mapping_line = f"0x{opcode_index:x}: 0x{address} = {final_name} ({confidence})"
            print(mapping_line)
            f.write(mapping_line + "\n")
            
            opcode_index += 1
    
    print(f"\nMapping saved to opcode_mapping.txt")
    print(f"Total opcodes mapped: {len(matches)}")

if __name__ == "__main__":
    main()
